import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, Brain, Zap, Target, Users, Building2, Heart, TrendingUp, Clock, Shield, Rocket } from "lucide-react";
import Footer from "@/components/Footer";
import { getIndustryUrls } from "@/lib/utils";

const GeneralHome = () => {
  const industryUrls = getIndustryUrls();

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section - Modern & Professional */}
      <section className="relative overflow-hidden min-h-screen flex items-center bg-gradient-to-br from-slate-50 via-white to-blue-50">
        {/* Subtle Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-100 rounded-full mix-blend-multiply filter blur-3xl opacity-40"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-indigo-100 rounded-full mix-blend-multiply filter blur-3xl opacity-40"></div>
        </div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-6xl mx-auto text-center">
            {/* Main Headline */}
            <div className="mb-12">
              <div className="inline-flex items-center gap-2 bg-blue-50 text-blue-700 border border-blue-200 rounded-full px-6 py-3 mb-8">
                <Brain className="w-5 h-5" />
                <span className="font-medium">AI-Powered Business Transformation</span>
              </div>

              <h1 className="text-5xl sm:text-6xl lg:text-7xl font-bold text-slate-900 mb-8 leading-tight">
                <span className="text-blue-600">Ascent AI</span>
                <br />
                <span className="text-3xl sm:text-4xl lg:text-5xl font-normal text-slate-600">
                  Intelligent Solutions for
                </span>
                <br />
                <span className="text-slate-900">Every Industry</span>
              </h1>
            </div>

            <p className="text-xl sm:text-2xl text-slate-600 mb-16 leading-relaxed max-w-4xl mx-auto">
              Transform your business with AI-powered automation that reduces costs, boosts efficiency, and drives sustainable growth.
            </p>

            {/* Industry Selection Cards - Sweet Spot Design */}
            <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto mb-16">
              {/* Healthcare Card */}
              <a href={industryUrls.clinic} className="group">
                <div className="relative bg-white border border-gray-200 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden">
                  {/* Subtle gradient overlay on hover */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-cyan-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                  <div className="relative z-10">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                      <Heart className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-slate-900 mb-4">Healthcare Clinics</h3>
                    <p className="text-slate-600 mb-6 leading-relaxed">
                      AI that runs your clinic so you don't have to. Reduce overheads, book more appointments, and eliminate admin chaos.
                    </p>
                    <div className="flex items-center text-blue-600 font-semibold group-hover:text-blue-700 transition-colors">
                      Explore Healthcare Solutions
                      <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                    </div>
                  </div>

                  {/* Decorative elements */}
                  <div className="absolute top-4 right-4 w-20 h-20 bg-blue-100 rounded-full opacity-20 group-hover:opacity-40 transition-opacity duration-500"></div>
                  <div className="absolute bottom-4 right-8 w-12 h-12 bg-cyan-100 rounded-full opacity-20 group-hover:opacity-40 transition-opacity duration-500 delay-100"></div>
                </div>
              </a>

              {/* Hotel Card */}
              <a href={industryUrls.hotel} className="group">
                <div className="relative bg-white border border-gray-200 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden">
                  {/* Subtle gradient overlay on hover */}
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-indigo-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                  <div className="relative z-10">
                    <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                      <Building2 className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-slate-900 mb-4">Hotels & Hospitality</h3>
                    <p className="text-slate-600 mb-6 leading-relaxed">
                      Future-proof your hotel operations with AI. Custom solutions that reduce no-shows, cut costs, and boost efficiency.
                    </p>
                    <div className="flex items-center text-purple-600 font-semibold group-hover:text-purple-700 transition-colors">
                      Explore Hotel Solutions
                      <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                    </div>
                  </div>

                  {/* Decorative elements */}
                  <div className="absolute top-4 right-4 w-20 h-20 bg-purple-100 rounded-full opacity-20 group-hover:opacity-40 transition-opacity duration-500"></div>
                  <div className="absolute bottom-4 right-8 w-12 h-12 bg-indigo-100 rounded-full opacity-20 group-hover:opacity-40 transition-opacity duration-500 delay-100"></div>
                </div>
              </a>
            </div>

            {/* Stats Section */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold text-slate-900 mb-2">80%</div>
                <div className="text-slate-600 text-sm">Admin Time Saved</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-slate-900 mb-2">2-4</div>
                <div className="text-slate-600 text-sm">Weeks to Deploy</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-slate-900 mb-2">24/7</div>
                <div className="text-slate-600 text-sm">AI Support</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-slate-900 mb-2">100%</div>
                <div className="text-slate-600 text-sm">Custom Built</div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-slate-300 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-slate-400 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Why Choose Ascent AI - Professional Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <div className="inline-flex items-center gap-2 bg-slate-100 text-slate-700 rounded-full px-6 py-3 mb-8">
                <Target className="w-5 h-5" />
                <span className="font-medium">Why Choose Ascent AI</span>
              </div>
              <h2 className="text-4xl sm:text-5xl font-bold text-slate-900 mb-6">
                Built for <span className="text-blue-600">Modern Businesses</span>
              </h2>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                We build custom AI solutions that automate repetitive tasks, streamline operations, and help your business achieve sustainable growth.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {/* Custom Solutions */}
              <div className="group">
                <div className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Target className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 mb-4">Custom Solutions</h3>
                  <p className="text-slate-600 leading-relaxed mb-6">
                    Tailored AI systems designed specifically for your business needs. No one-size-fits-all approaches.
                  </p>
                  <div className="flex items-center text-blue-600 font-medium group-hover:text-blue-700 transition-colors">
                    <span>100% Customized</span>
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </div>
                </div>
              </div>

              {/* Rapid Implementation */}
              <div className="group">
                <div className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
                  <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Zap className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 mb-4">Rapid Implementation</h3>
                  <p className="text-slate-600 leading-relaxed mb-6">
                    Get up and running in weeks, not months, with immediate ROI and measurable results.
                  </p>
                  <div className="flex items-center text-green-600 font-medium group-hover:text-green-700 transition-colors">
                    <span>2-4 Weeks Deploy</span>
                    <Clock className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </div>
                </div>
              </div>

              {/* Ongoing Support */}
              <div className="group">
                <div className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Shield className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 mb-4">Ongoing Support</h3>
                  <p className="text-slate-600 leading-relaxed mb-6">
                    Continuous optimization and support to ensure your success with 24/7 monitoring.
                  </p>
                  <div className="flex items-center text-purple-600 font-medium group-hover:text-purple-700 transition-colors">
                    <span>24/7 Support</span>
                    <Users className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </section>

      {/* Results & Impact Section */}
      <section className="py-20 bg-slate-900 text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto text-center">
            <h2 className="text-4xl sm:text-5xl font-bold mb-6">
              Proven Results Across Industries
            </h2>
            <p className="text-xl text-slate-300 mb-16 max-w-3xl mx-auto">
              Join businesses that have already transformed their operations with our AI solutions.
            </p>

            <div className="grid md:grid-cols-4 gap-8 mb-16">
              <div className="text-center">
                <div className="w-16 h-16 bg-slate-800 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <TrendingUp className="w-8 h-8 text-blue-400" />
                </div>
                <div className="text-3xl font-bold mb-2">300%</div>
                <div className="text-slate-400 text-sm">Average ROI Increase</div>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-slate-800 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Clock className="w-8 h-8 text-green-400" />
                </div>
                <div className="text-3xl font-bold mb-2">80%</div>
                <div className="text-slate-400 text-sm">Time Saved on Admin</div>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-slate-800 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Users className="w-8 h-8 text-indigo-400" />
                </div>
                <div className="text-3xl font-bold mb-2">500+</div>
                <div className="text-slate-400 text-sm">Happy Clients</div>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-slate-800 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Rocket className="w-8 h-8 text-purple-400" />
                </div>
                <div className="text-3xl font-bold mb-2">24/7</div>
                <div className="text-slate-400 text-sm">AI Operations</div>
              </div>
            </div>

            <div className="bg-slate-800 rounded-xl p-8 max-w-4xl mx-auto">
              <blockquote className="text-xl italic mb-6 text-slate-200">
                "Ascent AI didn't just automate our processes – they transformed our entire business model. We're now operating at a level we never thought possible."
              </blockquote>
              <div className="flex items-center justify-center gap-4">
                <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">MD</span>
                </div>
                <div className="text-left">
                  <div className="font-semibold text-white">Marc D.</div>
                  <div className="text-slate-400 text-sm">CEO, Founder</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 bg-gradient-to-br from-slate-50 to-blue-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 rounded-full px-6 py-3 mb-8">
              <Rocket className="w-5 h-5" />
              <span className="font-medium">Ready to Transform Your Business?</span>
            </div>

            <h2 className="text-4xl sm:text-5xl font-bold text-slate-900 mb-6">
              Start Your AI Journey <span className="text-blue-600">Today</span>
            </h2>

            <p className="text-xl text-slate-600 mb-12 leading-relaxed">
              Book a free strategy call and discover how AI can revolutionize your operations.
              No commitment, just insights tailored to your business.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12">
              <a href={industryUrls.clinic} className="group">
                <Button size="lg" className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white px-8 py-4 text-lg font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                  <Heart className="mr-3 h-6 w-6" />
                  Healthcare Solutions
                  <ArrowRight className="ml-3 h-6 w-6 group-hover:translate-x-1 transition-transform" />
                </Button>
              </a>

              <a href={industryUrls.hotel} className="group">
                <Button size="lg" className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white px-8 py-4 text-lg font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                  <Building2 className="mr-3 h-6 w-6" />
                  Hotel Solutions
                  <ArrowRight className="ml-3 h-6 w-6 group-hover:translate-x-1 transition-transform" />
                </Button>
              </a>
            </div>

            <div className="grid md:grid-cols-3 gap-6 max-w-3xl mx-auto">
              <div className="flex items-center justify-center gap-3 text-slate-600">
                <div className="w-8 h-8 bg-green-100 text-green-700 rounded-full flex items-center justify-center">
                  <span className="text-sm font-bold">✓</span>
                </div>
                <span>Free Strategy Call</span>
              </div>

              <div className="flex items-center justify-center gap-3 text-slate-600">
                <div className="w-8 h-8 bg-blue-100 text-blue-700 rounded-full flex items-center justify-center">
                  <span className="text-sm font-bold">✓</span>
                </div>
                <span>Custom Solution Design</span>
              </div>

              <div className="flex items-center justify-center gap-3 text-slate-600">
                <div className="w-8 h-8 bg-indigo-100 text-indigo-700 rounded-full flex items-center justify-center">
                  <span className="text-sm font-bold">✓</span>
                </div>
                <span>No Long-term Commitment</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default GeneralHome;
