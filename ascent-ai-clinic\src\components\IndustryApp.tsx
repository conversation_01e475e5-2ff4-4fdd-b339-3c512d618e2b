import React from "react";
import { Routes, Route } from "react-router-dom";
import { useIndustry } from "@/contexts/IndustryContext";
import Header from "./Header";
import ClinicPage from "@/pages/ClinicPage";
import HotelPage from "@/pages/HotelPage";
import HowItWorksPage from "@/pages/HowItWorksPage";
import AboutPage from "@/pages/AboutPage";
import FAQPage from "@/pages/FAQPage";
import BookCallPage from "@/pages/BookCallPage";
import NotFound from "@/pages/NotFound";
import VapiWidget from "./VapiWidget";
import GeneralHome from "@/pages/GeneralHome";
import IndustriesPage from "@/pages/IndustriesPage";

const IndustryApp: React.FC = () => {
  return (
    <>
      <VapiWidget />
      <Header />
      <Routes>
        {/* Clinic routes */}
        <Route path="/clinic" element={<ClinicPage />} />
        <Route path="/clinic/how-it-works" element={<HowItWorksPage />} />
        <Route path="/clinic/about" element={<AboutPage />} />
        <Route path="/clinic/faq" element={<FAQPage />} />
        <Route path="/clinic/book-call" element={<BookCallPage />} />

        {/* Hotel routes */}
        <Route path="/hotel" element={<HotelPage />} />
        <Route path="/hotel/how-it-works" element={<HowItWorksPage />} />
        <Route path="/hotel/about" element={<AboutPage />} />
        <Route path="/hotel/faq" element={<FAQPage />} />
        <Route path="/hotel/book-call" element={<BookCallPage />} />

        {/* General routes (legacy support) */}
        <Route path="/industries" element={<IndustriesPage />} />
        <Route path="/how-it-works" element={<HowItWorksPage />} />
        <Route path="/about" element={<AboutPage />} />
        <Route path="/faq" element={<FAQPage />} />
        <Route path="/book-call" element={<BookCallPage />} />

        {/* Fallback for unknown routes */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </>
  );
};

export default IndustryApp;
