[build]
  publish = "dist"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"

# Main domain redirects to clinic subdomain
[[redirects]]
  from = "/*"
  to = "https://clinic.ascent-ai.netlify.app/:splat"
  status = 301
  conditions = {Host = "ascent-ai.netlify.app"}

# Main domain redirects and headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Clinic subdomain redirects
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  conditions = {Host = "clinic.ascent-ai.netlify.app"}

# Hotel subdomain redirects  
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  conditions = {Host = "hotel.ascent-ai.netlify.app"}

# Custom domain redirects to clinic subdomain (when you connect your domain)
[[redirects]]
  from = "/*"
  to = "https://clinic.yourdomain.com/:splat"
  status = 301
  conditions = {Host = "yourdomain.com"}

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  conditions = {Host = "clinic.yourdomain.com"}

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  conditions = {Host = "hotel.yourdomain.com"}

# Security headers for all subdomains
[[headers]]
  for = "/*"
  [headers.values]
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://vapi.ai https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://ahfuiueotttbjlcuareb.supabase.co https://vapi.ai wss://vapi.ai;"

# Cache static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
