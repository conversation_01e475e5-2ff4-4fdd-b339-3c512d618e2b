import Footer from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, CheckCircle } from "lucide-react";
import { Link } from "react-router-dom";

const HotelPage = () => {
  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-blue-50 py-20 lg:py-32">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground mb-6 leading-tight">
              Future-Proof Your Hotel Operations
              <span className="block text-blue-600">with AI</span>
            </h1>

            <p className="text-xl sm:text-2xl text-muted-foreground mb-8 leading-relaxed">
              Custom AI solutions that reduce no-shows, cut costs, and boost efficiency without disrupting your team.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button asChild size="lg" className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700">
                <Link to="/book-call?industry=hotel">
                  🟦 Book Your Free AI Strategy Session
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>

        {/* Background decoration */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
          <div className="absolute top-3/4 right-1/4 w-64 h-64 bg-indigo-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse delay-1000"></div>
        </div>
      </section>

      {/* What We Do Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-6">
              Revolutionising Hotel Operations Through Bespoke AI Solutions
            </h2>
            <div className="text-lg text-muted-foreground space-y-6 text-left">
              <p>
                At Ascent AI, we help hotels cut costs, streamline operations, and improve guest satisfaction using smart, tailor-made AI technology.
              </p>
              <p>
                From front desk automation and voice agents to back-end process streamlining and predictive analytics, we design, develop, and implement solutions that eliminate inefficiencies and free up your staff to focus on high-impact guest experiences.
              </p>
              <p>
                Whether you're looking to fix a specific pain point or reimagine your operations with AI, we're your dedicated partner in hotel innovation.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Problems Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-6">
              We Solve the Costliest Problems Hotels Face Today
            </h2>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto mb-12">
            <div className="bg-white p-6 rounded-lg shadow-sm border-l-4 border-red-500">
              <h3 className="text-xl font-semibold mb-3 flex items-center">
                🔁 <span className="ml-2">No-Shows & Last-Minute Cancellations</span>
              </h3>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border-l-4 border-orange-500">
              <h3 className="text-xl font-semibold mb-3 flex items-center">
                🧾 <span className="ml-2">Inefficient Booking & Reservation Systems</span>
              </h3>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border-l-4 border-yellow-500">
              <h3 className="text-xl font-semibold mb-3 flex items-center">
                📊 <span className="ml-2">Manual and Time-Wasting Admin Work</span>
              </h3>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border-l-4 border-green-500">
              <h3 className="text-xl font-semibold mb-3 flex items-center">
                🧍‍♀️ <span className="ml-2">Understaffed Teams & Overworked Staff</span>
              </h3>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border-l-4 border-blue-500">
              <h3 className="text-xl font-semibold mb-3 flex items-center">
                🏨 <span className="ml-2">Guest Experience Gaps</span>
              </h3>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border-l-4 border-purple-500">
              <h3 className="text-xl font-semibold mb-3 flex items-center">
                💰 <span className="ml-2">Escalating Costs & Inefficiencies</span>
              </h3>
            </div>
          </div>

          <div className="text-center">
            <Button asChild variant="outline" size="lg">
              <Link to="/book-call?industry=hotel">
                🟦 Discover How We Solve These
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-6">
              Tailored AI Solutions Built for Hotels Like Yours
            </h2>
          </div>

          <div className="max-w-4xl mx-auto space-y-12">
            <div className="flex flex-col md:flex-row items-start gap-6">
              <div className="flex-shrink-0 w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center text-2xl font-bold">
                1
              </div>
              <div>
                <h3 className="text-2xl font-semibold mb-3">Discover</h3>
                <p className="text-lg text-muted-foreground">
                  We start with a deep dive into your hotel's current operations and pain points. This isn't a one-size-fits-all approach; we design around your unique needs.
                </p>
              </div>
            </div>

            <div className="flex flex-col md:flex-row items-start gap-6">
              <div className="flex-shrink-0 w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center text-2xl font-bold">
                2
              </div>
              <div>
                <h3 className="text-2xl font-semibold mb-3">Prototype</h3>
                <p className="text-lg text-muted-foreground">
                  We craft tailored AI workflows and interface mockups to give you a clear vision of how your solution will function.
                </p>
              </div>
            </div>

            <div className="flex flex-col md:flex-row items-start gap-6">
              <div className="flex-shrink-0 w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center text-2xl font-bold">
                3
              </div>
              <div>
                <h3 className="text-2xl font-semibold mb-3">Develop</h3>
                <p className="text-lg text-muted-foreground">
                  Our team of expert engineers builds, tests, and refines the tool until it's ready for seamless integration into your daily operations.
                </p>
              </div>
            </div>

            <div className="flex flex-col md:flex-row items-start gap-6">
              <div className="flex-shrink-0 w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center text-2xl font-bold">
                4
              </div>
              <div>
                <h3 className="text-2xl font-semibold mb-3">Deploy</h3>
                <p className="text-lg text-muted-foreground">
                  We handle deployment and provide your staff with the training needed to use the new system with ease.
                </p>
              </div>
            </div>

            <div className="flex flex-col md:flex-row items-start gap-6">
              <div className="flex-shrink-0 w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center text-2xl font-bold">
                5
              </div>
              <div>
                <h3 className="text-2xl font-semibold mb-3">Scale</h3>
                <p className="text-lg text-muted-foreground">
                  We stay with you post-launch, improving, scaling, and adapting your AI solution as your business grows.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-20 bg-blue-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-6">
              Not Just Another Tech Vendor — We Are Your Long-Term Innovation Partner
            </h2>
          </div>

          <div className="max-w-4xl mx-auto space-y-8">
            <div className="flex items-start gap-4">
              <CheckCircle className="w-6 h-6 text-green-600 flex-shrink-0 mt-1" />
              <div>
                <h3 className="text-xl font-semibold mb-2">Built for Hotels, by AI Experts</h3>
                <p className="text-muted-foreground">
                  We understand hospitality inside and out. Our solutions are crafted specifically for hotels, not adapted from other industries.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <CheckCircle className="w-6 h-6 text-green-600 flex-shrink-0 mt-1" />
              <div>
                <h3 className="text-xl font-semibold mb-2">Fully Custom-Built Tools</h3>
                <p className="text-muted-foreground">
                  Every hotel is different. That's why we build tools designed around your team, your goals, and your operations.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <CheckCircle className="w-6 h-6 text-green-600 flex-shrink-0 mt-1" />
              <div>
                <h3 className="text-xl font-semibold mb-2">End-to-End Support</h3>
                <p className="text-muted-foreground">
                  We guide you through every stage of development, from brainstorming to deployment and beyond.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <CheckCircle className="w-6 h-6 text-green-600 flex-shrink-0 mt-1" />
              <div>
                <h3 className="text-xl font-semibold mb-2">Rapid Deployment, Real Results</h3>
                <p className="text-muted-foreground">
                  Our lean, agile processes allow you to start seeing impact in weeks.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <CheckCircle className="w-6 h-6 text-green-600 flex-shrink-0 mt-1" />
              <div>
                <h3 className="text-xl font-semibold mb-2">Future-Proofing Your Business</h3>
                <p className="text-muted-foreground">
                  AI evolves fast. We keep you ahead of the curve with ongoing updates, strategic reviews, and improvement suggestions.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Solutions Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-6">
              AI Modules Designed for the Modern Hotel
            </h2>
            <p className="text-lg text-muted-foreground">
              Choose one or combine several, every tool is fully customisable.
            </p>
          </div>

          <div className="max-w-6xl mx-auto">
            <div className="overflow-x-auto">
              <table className="w-full border-collapse bg-white shadow-lg rounded-lg overflow-hidden">
                <thead>
                  <tr className="bg-blue-600 text-white">
                    <th className="px-6 py-4 text-left font-semibold">Module</th>
                    <th className="px-6 py-4 text-left font-semibold">What It Does</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b border-gray-200 hover:bg-gray-50">
                    <td className="px-6 py-4 font-semibold">AI Voice Agent</td>
                    <td className="px-6 py-4 text-muted-foreground">Confirms bookings, handles FAQs, and manages reschedules 24/7.</td>
                  </tr>
                  <tr className="border-b border-gray-200 hover:bg-gray-50">
                    <td className="px-6 py-4 font-semibold">Smart Front Desk Assistant</td>
                    <td className="px-6 py-4 text-muted-foreground">Automates guest check-in/out, concierge services, and ID verification.</td>
                  </tr>
                  <tr className="border-b border-gray-200 hover:bg-gray-50">
                    <td className="px-6 py-4 font-semibold">Workflow Automation</td>
                    <td className="px-6 py-4 text-muted-foreground">Replaces paper trails and spreadsheets with streamlined digital processes.</td>
                  </tr>
                  <tr className="border-b border-gray-200 hover:bg-gray-50">
                    <td className="px-6 py-4 font-semibold">Guest Insights Engine</td>
                    <td className="px-6 py-4 text-muted-foreground">Personalises guest experiences based on past behaviours and preferences.</td>
                  </tr>
                  <tr className="border-b border-gray-200 hover:bg-gray-50">
                    <td className="px-6 py-4 font-semibold">Predictive Booking Analytics</td>
                    <td className="px-6 py-4 text-muted-foreground">Anticipates cancellations, demand trends, and price fluctuations.</td>
                  </tr>
                  <tr className="hover:bg-gray-50">
                    <td className="px-6 py-4 font-semibold">Automated Task Manager</td>
                    <td className="px-6 py-4 text-muted-foreground">Delegates tasks, tracks progress, and sends reminders to hotel staff.</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>

      {/* Success Stories Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="grid md:grid-cols-2 gap-8">
              <div className="bg-white p-8 rounded-lg shadow-sm">
                <p className="text-lg text-muted-foreground mb-6 italic">
                  "Ascent AI helped us cut our no-show rate by 40% and saved hundreds of staff hours a month. The custom voice agent was a game-changer."
                </p>
                <p className="font-semibold text-foreground">
                  — General Manager, 4-Star Hotel in Singapore
                </p>
              </div>

              <div className="bg-white p-8 rounded-lg shadow-sm">
                <p className="text-lg text-muted-foreground mb-6 italic">
                  "We've never had such a seamless integration of tech into our front desk operations. The time we save is now spent delighting guests."
                </p>
                <p className="font-semibold text-foreground">
                  — Operations Head, Boutique Hotel
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-6">
              Frequently Asked Questions
            </h2>
          </div>

          <div className="max-w-4xl mx-auto space-y-8">
            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-xl font-semibold mb-3">Is AI hard to implement in my hotel?</h3>
              <p className="text-muted-foreground">
                Not at all. We do all the heavy lifting, from integration to training your staff.
              </p>
            </div>

            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-xl font-semibold mb-3">Do I need to hire new IT staff?</h3>
              <p className="text-muted-foreground">
                No. Our systems are designed to be user-friendly and require minimal technical maintenance.
              </p>
            </div>

            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-xl font-semibold mb-3">Can your AI work with our existing hotel management system (PMS)?</h3>
              <p className="text-muted-foreground">
                Yes. We integrate seamlessly with most modern PMS and CRM platforms.
              </p>
            </div>

            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-xl font-semibold mb-3">Is it expensive?</h3>
              <p className="text-muted-foreground">
                Our solutions are priced based on outcomes and ROI. Most clients see cost savings within the first few months.
              </p>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default HotelPage;