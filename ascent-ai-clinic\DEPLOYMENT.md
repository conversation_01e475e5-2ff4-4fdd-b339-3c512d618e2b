# Subdomain Deployment Guide for Netlify

This guide explains how to deploy the multi-industry application with subdomain support on Netlify.

## Overview

The application is designed to serve different industries on separate subdomains:
- `yourdomain.com` - Redirects to clinic subdomain (301 redirect)
- `clinic.yourdomain.com` - Clinic-specific application (primary)
- `hotel.yourdomain.com` - Hotel-specific application

## Netlify Deployment Steps

### 1. Initial Deployment

1. Connect your repository to Netlify
2. Set build command: `npm run build`
3. Set publish directory: `dist`
4. Deploy the site

### 2. Custom Domain Setup

1. In Netlify dashboard, go to **Site settings > Domain management**
2. Click **Add custom domain**
3. Enter your main domain: `yourdomain.com`
4. Follow DNS configuration instructions

### 3. Subdomain Configuration

#### Option A: DNS Configuration (Recommended)
1. In your DNS provider, add CNAME records:
   ```
   clinic.yourdomain.com -> your-site-name.netlify.app
   hotel.yourdomain.com -> your-site-name.netlify.app
   ```

#### Option B: Netlify Domain Aliases
1. In Netlify dashboard, go to **Site settings > Domain management**
2. Click **Add domain alias**
3. Add each subdomain:
   - `clinic.yourdomain.com`
   - `hotel.yourdomain.com`

### 4. SSL Certificate

Netlify automatically provisions SSL certificates for your custom domain and subdomains.

## Environment Variables

No additional environment variables are required. The application automatically detects the subdomain and serves the appropriate content.

## Testing Subdomains

### Development Testing
Use query parameters to simulate subdomains:
- `http://localhost:8080?industry=clinic`
- `http://localhost:8080?industry=hotel`
- `http://localhost:8080` (general)

### Production Testing
Once deployed, test each subdomain:
- `https://yourdomain.com` (redirects to clinic subdomain)
- `https://clinic.yourdomain.com` (primary clinic application)
- `https://hotel.yourdomain.com`

## Netlify Configuration Files

The repository includes:
- `netlify.toml` - Main configuration with redirects and headers
- `public/_redirects` - Backup redirect configuration

## Subdomain Detection Logic

The application uses the following detection logic:

1. **Development**: Query parameter `?industry=clinic|hotel`
2. **Production**: Subdomain parsing from `window.location.hostname`
3. **Fallback**: Path-based detection for compatibility

## Industry Isolation Features

Each subdomain provides complete industry isolation:

- **Separate Navigation**: Industry-specific menu items
- **Isolated Content**: No cross-industry references
- **Industry Branding**: Specific colors and messaging
- **SEO Optimization**: Industry-specific meta tags and titles
- **Independent Routing**: Separate route configurations

## Troubleshooting

### Subdomain Not Working
1. Check DNS propagation (can take up to 48 hours)
2. Verify CNAME records are correct
3. Check Netlify domain aliases configuration

### Content Not Switching
1. Clear browser cache
2. Check browser developer tools for JavaScript errors
3. Verify subdomain detection in browser console

### SSL Issues
1. Wait for automatic SSL provisioning (can take a few minutes)
2. Check that all subdomains are added to Netlify
3. Force SSL renewal in Netlify dashboard if needed

## Branch Deployments

For branch deployments, Netlify creates URLs like:
- `branch-name--your-site.netlify.app`

The subdomain detection handles these patterns automatically.

## Custom Domain Examples

Replace `yourdomain.com` with your actual domain:
- Main: `ascent-ai.com` (redirects to clinic.ascent-ai.com)
- Clinic: `clinic.ascent-ai.com` (primary application)
- Hotel: `hotel.ascent-ai.com`

## Support

For additional help:
1. Check Netlify documentation on custom domains
2. Verify DNS configuration with your domain provider
3. Test subdomain detection in browser developer tools
