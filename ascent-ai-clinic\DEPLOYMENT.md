# Subdirectory Deployment Guide for Netlify

This guide explains how to deploy the multi-industry application with subdirectory-based routing on Netlify.

## Overview

The application is designed to serve different industries on separate subdirectories:
- `yourdomain.com` - Redirects to `/clinic` (301 redirect)
- `yourdomain.com/clinic` - Clinic-specific application (primary)
- `yourdomain.com/hotel` - Hotel-specific application

## Netlify Deployment Steps

### 1. Initial Deployment

1. Connect your repository to Netlify
2. Set build command: `npm run build`
3. Set publish directory: `dist`
4. Deploy the site

### 2. Custom Domain Setup (Optional)

1. In Netlify dashboard, go to **Site settings > Domain management**
2. Click **Add custom domain**
3. Enter your main domain: `yourdomain.com`
4. Follow DNS configuration instructions

### 3. SSL Certificate

Netlify automatically provisions SSL certificates for your custom domain.

## Environment Variables

No additional environment variables are required. The application automatically detects the URL path and serves the appropriate content.

## Testing Subdirectories

### Development Testing
Test different industry paths:
- `http://localhost:8080/clinic` (clinic application)
- `http://localhost:8080/hotel` (hotel application)
- `http://localhost:8080` (redirects to clinic)

### Production Testing
Once deployed, test each path:
- `https://yourdomain.com` (redirects to `/clinic`)
- `https://yourdomain.com/clinic` (primary clinic application)
- `https://yourdomain.com/hotel` (hotel application)

## Netlify Configuration Files

The repository includes:
- `netlify.toml` - Main configuration with redirects and headers
- `public/_redirects` - Backup redirect configuration

## Path Detection Logic

The application uses the following detection logic:

1. **All Environments**: URL path parsing from `window.location.pathname`
2. **Development Fallback**: Query parameter `?industry=clinic|hotel` for backward compatibility
3. **Default**: Defaults to clinic when no specific path is detected

## Industry Isolation Features

Each subdirectory provides complete industry isolation:

- **Separate Navigation**: Industry-specific menu items
- **Isolated Content**: No cross-industry references
- **Industry Branding**: Specific colors and messaging
- **SEO Optimization**: Industry-specific meta tags and titles
- **Independent Routing**: Separate route configurations

## Troubleshooting

### Routing Not Working
1. Clear browser cache and hard refresh
2. Check browser developer tools for JavaScript errors
3. Verify path detection in browser console
4. Ensure Netlify redirects are properly configured

### Content Not Switching
1. Clear browser cache
2. Check browser developer tools for JavaScript errors
3. Verify path detection in browser console: `window.location.pathname`

### SSL Issues
1. Wait for automatic SSL provisioning (can take a few minutes)
2. Force SSL renewal in Netlify dashboard if needed

## Branch Deployments

For branch deployments, Netlify creates URLs like:
- `branch-name--your-site.netlify.app`

The path detection works the same way on branch deployments.

## Custom Domain Examples

Replace `yourdomain.com` with your actual domain:
- Main: `ascent-ai.com` (redirects to `/clinic`)
- Clinic: `ascent-ai.com/clinic` (primary application)
- Hotel: `ascent-ai.com/hotel`

## Support

For additional help:
1. Check Netlify documentation on redirects and SPA routing
2. Verify DNS configuration with your domain provider (if using custom domain)
3. Test path detection in browser developer tools: `console.log(window.location.pathname)`
