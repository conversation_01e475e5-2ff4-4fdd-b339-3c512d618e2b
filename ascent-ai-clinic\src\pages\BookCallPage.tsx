
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Calendar, Clock, Phone, Mail, User, Building } from "lucide-react";
import { useState, useEffect } from "react";
import { useBookCallSubmission } from "@/hooks/useBookCallSubmission";
import { useIndustry, useIndustryContext } from "@/contexts/IndustryContext";

const BookCallPage = () => {
  // Use direct industry context for better path-based detection
  const { industry, branding } = useIndustry();
  const { isClinic, isHotel, isGeneral } = useIndustryContext();

  // Debug logging to check industry detection
  useEffect(() => {
    console.log('BookCallPage - Current path:', window.location.pathname);
    console.log('BookCallPage - Detected industry:', industry);
    console.log('BookCallPage - isClinic:', isClinic);
    console.log('BookCallPage - isHotel:', isHotel);
  }, [industry, isClinic, isHotel]);

  // Industry-specific configuration
  const getBookCallConfig = () => {
    if (isClinic) {
      return {
        industryName: "clinic",
        businessLabel: "Clinic Name",
        businessPlaceholder: "Smith Medical Center",
        messagePrompt: "Brief description of your clinic size, current challenges, and what you hope to achieve with AI automation...",
        heroText: "Ready to transform your clinic with AI? Let's discuss how we can help you reduce overheads, book more appointments, and eliminate admin chaos.",
        assessmentText: "We'll analyze your current clinic operations and identify automation opportunities.",
        nextStepsText: "Clear roadmap for implementing AI solutions in your clinic.",
      };
    } else if (isHotel) {
      return {
        industryName: "hotel",
        businessLabel: "Hotel Name",
        businessPlaceholder: "Grand Hotel & Suites",
        messagePrompt: "Brief description of your hotel size, current challenges, and what you hope to achieve with AI automation...",
        heroText: "Ready to future-proof your hotel with AI? Let's discuss how we can help you reduce no-shows, cut costs, and boost efficiency.",
        assessmentText: "We'll analyze your current hotel operations and identify automation opportunities.",
        nextStepsText: "Clear roadmap for implementing AI solutions in your hotel.",
      };
    } else {
      return {
        industryName: "business",
        businessLabel: "Business Name",
        businessPlaceholder: "Your Business Name",
        messagePrompt: "Brief description of your business, current challenges, and what you hope to achieve with AI automation...",
        heroText: "Ready to transform your business with AI? Let's discuss how we can help you streamline operations and boost efficiency.",
        assessmentText: "We'll analyze your current business operations and identify automation opportunities.",
        nextStepsText: "Clear roadmap for implementing AI solutions in your business.",
      };
    }
  };

  const config = getBookCallConfig();

  // Extract configuration values
  const {
    industryName,
    businessLabel,
    businessPlaceholder,
    messagePrompt,
    heroText,
    assessmentText,
    nextStepsText
  } = config;
  
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    business: "",
    preferredDate: "",
    preferredTime: "",
    message: ""
  });

  const { submitForm, isSubmitting } = useBookCallSubmission();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const result = await submitForm(formData, industry);
    
    if (result.success) {
      // Reset form on success
      setFormData({
        name: "",
        email: "",
        phone: "",
        business: "",
        preferredDate: "",
        preferredTime: "",
        message: ""
      });
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  // Get industry-specific background gradient
  const getBackgroundGradient = () => {
    if (isClinic) return "bg-gradient-to-br from-blue-50 via-white to-blue-50";
    if (isHotel) return "bg-gradient-to-br from-indigo-50 via-white to-indigo-50";
    return "bg-gradient-to-br from-slate-50 via-white to-slate-50";
  };

  // Get industry-specific button color
  const getButtonColor = () => {
    if (isClinic) return "bg-blue-600 hover:bg-blue-700";
    if (isHotel) return "bg-indigo-600 hover:bg-indigo-700";
    return "bg-slate-600 hover:bg-slate-700";
  };

  // Get industry-specific accent colors
  const getAccentColors = () => {
    if (isClinic) return { bg: "bg-blue-100", text: "text-blue-600", accent: "bg-blue-50" };
    if (isHotel) return { bg: "bg-indigo-100", text: "text-indigo-600", accent: "bg-indigo-50" };
    return { bg: "bg-slate-100", text: "text-slate-600", accent: "bg-slate-50" };
  };

  const accentColors = getAccentColors();

  return (
    <div className="min-h-screen bg-background">
      <section className={`py-20 lg:py-32 ${getBackgroundGradient()}`}>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            {/* Header */}
            <div className="text-center mb-12">
              <h1 className="text-4xl sm:text-5xl font-bold text-foreground mb-6">
                Book Your Free
                <span className={`block ${accentColors.text}`}>Strategy Call</span>
              </h1>
              <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
                {heroText}
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-12 items-start">
              {/* Form */}
              <div className="bg-white rounded-lg shadow-lg p-8">
                <h2 className="text-2xl font-bold text-foreground mb-6">Schedule Your Call</h2>
                
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid sm:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name" className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        Full Name *
                      </Label>
                      <Input
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder={isClinic ? "Dr. John Smith" : "John Smith"}
                        required
                        disabled={isSubmitting}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="email" className="flex items-center gap-2">
                        <Mail className="h-4 w-4" />
                        Email Address *
                      </Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        placeholder={isClinic ? "<EMAIL>" : "<EMAIL>"}
                        required
                        disabled={isSubmitting}
                      />
                    </div>
                  </div>

                  <div className="grid sm:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="phone" className="flex items-center gap-2">
                        <Phone className="h-4 w-4" />
                        Phone Number *
                      </Label>
                      <Input
                        id="phone"
                        name="phone"
                        type="tel"
                        value={formData.phone}
                        onChange={handleInputChange}
                        placeholder="+****************"
                        required
                        disabled={isSubmitting}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="business" className="flex items-center gap-2">
                        <Building className="h-4 w-4" />
                        {businessLabel} *
                      </Label>
                      <Input
                        id="business"
                        name="business"
                        value={formData.business}
                        onChange={handleInputChange}
                        placeholder={businessPlaceholder}
                        required
                        disabled={isSubmitting}
                      />
                    </div>
                  </div>

                  <div className="grid sm:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="preferredDate" className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        Preferred Date
                      </Label>
                      <Input
                        id="preferredDate"
                        name="preferredDate"
                        type="date"
                        value={formData.preferredDate}
                        onChange={handleInputChange}
                        disabled={isSubmitting}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="preferredTime" className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        Preferred Time
                      </Label>
                      <Input
                        id="preferredTime"
                        name="preferredTime"
                        type="time"
                        value={formData.preferredTime}
                        onChange={handleInputChange}
                        disabled={isSubmitting}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="message">
                      Tell us about your {industryName}
                    </Label>
                    <Textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      placeholder={messagePrompt}
                      className="min-h-[120px]"
                      disabled={isSubmitting}
                    />
                  </div>

                  <Button
                    type="submit"
                    size="lg"
                    className={`w-full ${getButtonColor()} text-white font-semibold py-4`}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Submitting..." : "Book My Free Strategy Call"}
                  </Button>
                </form>
              </div>

              {/* Info Panel */}
              <div className="space-y-8">
                <div className="bg-white rounded-lg shadow-lg p-8">
                  <h3 className="text-2xl font-bold text-foreground mb-6">What to Expect</h3>
                  <div className="space-y-4">
                    <div className="flex items-start gap-3">
                      <div className={`w-8 h-8 ${accentColors.bg} rounded-full flex items-center justify-center flex-shrink-0 mt-1`}>
                        <span className={`${accentColors.text} font-bold text-sm`}>1</span>
                      </div>
                      <div>
                        <h4 className="font-semibold text-foreground">Assessment</h4>
                        <p className="text-muted-foreground text-sm">
                          {assessmentText}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-start gap-3">
                      <div className={`w-8 h-8 ${accentColors.bg} rounded-full flex items-center justify-center flex-shrink-0 mt-1`}>
                        <span className={`${accentColors.text} font-bold text-sm`}>2</span>
                      </div>
                      <div>
                        <h4 className="font-semibold text-foreground">Strategy</h4>
                        <p className="text-muted-foreground text-sm">Custom AI implementation plan tailored to your specific needs and goals.</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start gap-3">
                      <div className={`w-8 h-8 ${accentColors.bg} rounded-full flex items-center justify-center flex-shrink-0 mt-1`}>
                        <span className={`${accentColors.text} font-bold text-sm`}>3</span>
                      </div>
                      <div>
                        <h4 className="font-semibold text-foreground">Next Steps</h4>
                        <p className="text-muted-foreground text-sm">
                          {nextStepsText}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className={`${accentColors.accent} rounded-lg p-6`}>
                  <h4 className="font-bold text-foreground mb-3">Call Details</h4>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    <li className="flex items-center gap-2">
                      <Clock className={`h-4 w-4 ${accentColors.text}`} />
                      30-45 minutes
                    </li>
                    <li className="flex items-center gap-2">
                      <Phone className={`h-4 w-4 ${accentColors.text}`} />
                      Video call via Zoom
                    </li>
                    <li className="flex items-center gap-2">
                      <User className={`h-4 w-4 ${accentColors.text}`} />
                      One-on-one with our AI specialist
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <Footer />
    </div>
  );
};

export default BookCallPage;
