import React, { createContext, useContext, useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { Industry, getIndustryFromPath, getIndustryBranding, getIndustryNavigation } from '@/lib/utils';

interface IndustryContextType {
  industry: Industry;
  branding: ReturnType<typeof getIndustryBranding>;
  navigation: ReturnType<typeof getIndustryNavigation>;
  isLoading: boolean;
}

const IndustryContext = createContext<IndustryContextType | undefined>(undefined);

export function IndustryProvider({ children }: { children: React.ReactNode }) {
  const location = useLocation();
  const [industry, setIndustry] = useState<Industry>('clinic');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Detect industry from current path
    const detectedIndustry = getIndustryFromPath();
    setIndustry(detectedIndustry);
    setIsLoading(false);
  }, [location.pathname]); // Re-run when path changes

  const branding = getIndustryBranding(industry);
  const navigation = getIndustryNavigation(industry);

  const value: IndustryContextType = {
    industry,
    branding,
    navigation,
    isLoading,
  };

  return (
    <IndustryContext.Provider value={value}>
      {children}
    </IndustryContext.Provider>
  );
}

export function useIndustry() {
  const context = useContext(IndustryContext);
  if (context === undefined) {
    throw new Error('useIndustry must be used within an IndustryProvider');
  }
  return context;
}

// Hook for checking specific industry contexts
export function useIndustryContext() {
  const { industry } = useIndustry();
  
  return {
    isClinic: industry === 'clinic',
    isHotel: industry === 'hotel',
    isGeneral: industry === 'general',
    industry,
  };
}
