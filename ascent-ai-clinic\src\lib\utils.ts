import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Industry types
export type Industry = 'clinic' | 'hotel' | 'general';

// Path-based industry detection utility
export function getIndustryFromPath(): Industry {
  if (typeof window === 'undefined') return 'clinic'; // Default to clinic for SSR

  const path = window.location.pathname;

  // Check for industry-specific paths
  if (path.startsWith('/clinic') || path === '/clinic') return 'clinic';
  if (path.startsWith('/hotel') || path === '/hotel') return 'hotel';

  // For development, also check query params for backward compatibility
  const urlParams = new URLSearchParams(window.location.search);
  const industryParam = urlParams.get('industry');
  if (industryParam === 'clinic' || industryParam === 'hotel') {
    return industryParam as Industry;
  }

  // Default to clinic (since root redirects to clinic)
  return 'clinic';
}

// Legacy function name for backward compatibility
export function getIndustryFromSubdomain(): Industry {
  return getIndustryFromPath();
}

// Get industry-specific branding
export function getIndustryBranding(industry: Industry) {
  switch (industry) {
    case 'clinic':
      return {
        name: 'Ascent AI Clinic',
        tagline: 'AI That Runs Your Clinic. So You Don\'t Have To.',
        primaryColor: 'blue',
        description: 'Reduce overheads, book more appointments, and eliminate admin chaos.',
        colorScheme: {
          primary: 'blue-600',
          primaryHover: 'blue-700',
          accent: 'blue-50',
          gradient: 'from-blue-50 via-white to-blue-50',
        },
      };
    case 'hotel':
      return {
        name: 'Ascent AI Hotel',
        tagline: 'Future-Proof Your Hotel Operations with AI',
        primaryColor: 'indigo',
        description: 'Custom AI solutions that reduce no-shows, cut costs, and boost efficiency.',
        colorScheme: {
          primary: 'indigo-600',
          primaryHover: 'indigo-700',
          accent: 'indigo-50',
          gradient: 'from-indigo-50 via-white to-indigo-50',
        },
      };
    default:
      return {
        name: 'Ascent AI',
        tagline: 'AI That Runs Your Business. So You Don\'t Have To.',
        primaryColor: 'blue',
        description: 'Transform your business operations with intelligent automation.',
        colorScheme: {
          primary: 'blue-600',
          primaryHover: 'blue-700',
          accent: 'blue-50',
          gradient: 'from-blue-50 via-white to-blue-50',
        },
      };
  }
}

// Get industry-specific navigation items
export function getIndustryNavigation(industry: Industry) {
  switch (industry) {
    case 'clinic':
      return [
        { name: "Home", path: "/clinic" },
        { name: "How It Works", path: "/clinic/how-it-works" },
        { name: "About", path: "/clinic/about" },
        { name: "FAQ", path: "/clinic/faq" }
      ];
    case 'hotel':
      return [
        { name: "Home", path: "/hotel" },
        { name: "How It Works", path: "/hotel/how-it-works" },
        { name: "About", path: "/hotel/about" },
        { name: "FAQ", path: "/hotel/faq" }
      ];
    default:
      return [
        { name: "Home", path: "/" },
        { name: "Industries", path: "/industries" },
        { name: "How It Works", path: "/how-it-works" },
        { name: "About", path: "/about" },
        { name: "FAQ", path: "/faq" }
      ];
  }
}

// Get subdirectory URLs for cross-industry navigation
export function getIndustryUrls() {
  return {
    clinic: '/clinic',
    hotel: '/hotel',
    main: '/clinic' // Root redirects to clinic
  };
}

// Legacy function name for backward compatibility
export function getSubdomainUrls() {
  return getIndustryUrls();
}
